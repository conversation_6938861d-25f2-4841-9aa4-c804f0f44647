# AI Guide: Implementering av Nya RPA-Steg

## Översikt

Denna guide förklarar hur nya RPA-steg implementeras i systemet. RPA-systemet använder en modulär arkitektur med stegtyper, runners och UI-komponenter som arbetar tillsammans.

## Arkitektur

### 1. Stegtyper och Interfaces

Alla RPA-steg definieras i `shared/src/types.ts` och följer detta mönster:

```typescript
// Bas-interface för alla steg
export interface RpaStepBase {
  id: string;
  type: string;
  description?: string;
  timeout?: number; // milliseconds, default 30000
}

// Specifikt steg-interface
export interface ExempelStep extends RpaStepBase {
  type: 'exempel';
  selector: string;
  value?: string;
  variableName?: string; // För steg som skapar variabler
}
```

**Viktiga punkter:**
- Alla steg måste utöka `RpaStepBase`
- `type` måste vara en unik sträng som identifierar stegtypen
- <PERSON>ägg till `variableName?: string` för steg som skapar variabler
- Lägg till steget i `RpaStep` union-typen

### 2. Runner-System

Systemet använder en factory-pattern för runners:

#### Stegkategorisering (`backend/src/runner/stepTypes.ts`)
```typescript
// Lägg till stegtypen i rätt kategori
export const WEB_AUTOMATION_STEPS = [
  'navigate', 'click', 'fill', 'exempel' // Lägg till här
] as const;

// Mappning skapas automatiskt
export const STEP_TO_RUNNER_MAP: Record<string, RunnerType> = {
  ...Object.fromEntries(WEB_AUTOMATION_STEPS.map(step => [step, RunnerType.PLAYWRIGHT]))
};
```

#### Runner-Implementation
Steg implementeras i lämplig runner (t.ex. `backend/src/runner/playwrightRunner.ts`):

```typescript
case 'exempel':
  const interpolatedSelector = interpolateVariables(step.selector, context.variables);
  const interpolatedValue = interpolateVariables(step.value || '', context.variables);
  
  // Utför stegets logik
  await page.fill(interpolatedSelector, interpolatedValue);
  
  // Om steget skapar en variabel
  if (step.variableName) {
    const result = await page.textContent(interpolatedSelector);
    variables[step.variableName] = result;
    context.variables[step.variableName] = result;
    
    onLog({
      level: 'info',
      message: `Exempel step completed: ${result}`,
      stepId: step.id,
      data: { [step.variableName]: result }
    });
  }
  break;
```

### 3. UI-Komponenter

#### StepToolbar (`frontend/src/components/flow-editor/StepToolbar.tsx`)
Lägg till steget i rätt kategori:

```typescript
const stepCategories: StepCategory[] = [
  {
    name: 'Interactions',
    icon: '👆',
    steps: [
      // ... befintliga steg
      {
        type: 'exempel',
        name: 'Exempel',
        icon: '⚡',
        description: 'Beskrivning av vad steget gör'
      }
    ]
  }
];
```

#### StepEditor (`frontend/src/components/flow-editor/StepEditor.tsx`)
Lägg till redigeringsformulär:

```typescript
case 'exempel':
  return (
    <>
      {renderFormField('Element Selector',
        <VariableInput
          value={editedStep.selector || ''}
          onChange={(value) => updateStep({ selector: value })}
          placeholder="button, #id, .class"
          steps={steps}
          currentStepIndex={currentStepIndex}
          style={inputStyle}
        />
      )}
      {renderFormField('Värde',
        <VariableInput
          value={editedStep.value || ''}
          onChange={(value) => updateStep({ value: value })}
          placeholder="Värde att använda"
          steps={steps}
          currentStepIndex={currentStepIndex}
          style={inputStyle}
        />
      )}
      {editedStep.variableName !== undefined && renderFormField('Variabelnamn',
        <input
          type="text"
          style={inputStyle}
          value={editedStep.variableName || ''}
          onChange={(e) => updateStep({ variableName: e.target.value })}
          placeholder="variabelNamn"
        />
      )}
    </>
  )
```

#### Ikoner och Färger
Lägg till ikoner i flera komponenter:

**FlowDesigner.tsx:**
```typescript
const getStepIcon = (stepType: string) => {
  switch (stepType) {
    case 'exempel':
      return { icon: '⚡', color: '#10b981' }
    // ...
  }
}
```

**RpaStepNode.tsx:**
```typescript
const getStepIcon = (stepType: string): string => {
  switch (stepType) {
    case 'exempel': return '⚡'
    // ...
  }
}
```

### 4. Variabelhantering

#### Variabelskapande Steg
För steg som skapar variabler:

1. **Lägg till `variableName` i interface**
2. **Implementera i runner** med variabeltilldelning
3. **Uppdatera VariableHelper** för UI-stöd:

```typescript
// I VariableHelper.tsx
if (step.type === 'exempel' && (step as any).variableName) {
  variables.push({
    name: extractStep.variableName,
    stepIndex: i,
    stepType: step.type,
    description: step.description
  });
}
```

4. **Uppdatera VariablesModal** för förhandsvisning:

```typescript
// I VariablesModal.tsx
if (step.type === 'exempel' && (step as any).variableName) {
  expectedVars[(step as any).variableName] = `Exempel data från: ${(step as any).selector}`
}
```

#### Variabelanvändning
Alla steg kan använda variabler via `${variableName}` syntax:
- Använd `VariableInput` komponenten för fält som stöder variabler
- Implementera `interpolateVariables()` i runner för variabelersättning

### 5. Validering

Lägg till validering i `shared/src/types.ts`:

```typescript
export function validateStep(step: RpaStep): { valid: boolean; errors: string[] } {
  // ... befintlig validering
  
  if (step.type === 'exempel') {
    const exempelStep = step as ExempelStep;
    if (!exempelStep.selector?.trim()) {
      errors.push('Selector is required');
    }
    if (exempelStep.variableName && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(exempelStep.variableName)) {
      errors.push('Variable name must be a valid identifier');
    }
  }
}
```

### 6. Stegetiketter

Uppdatera `getStepLabel()` funktionen i `shared/src/types.ts`:

```typescript
export function getStepLabel(step: RpaStep): string {
  switch (step.type) {
    case 'exempel':
      return `Exempel: ${step.selector || 'element'}`
    // ...
  }
}
```

## Implementeringschecklista

När du implementerar ett nytt steg:

### Backend:
- [ ] Lägg till interface i `shared/src/types.ts`
- [ ] Lägg till i `RpaStep` union-typ
- [ ] Lägg till i rätt kategori i `stepTypes.ts`
- [ ] Implementera i lämplig runner
- [ ] Lägg till validering
- [ ] Uppdatera `getStepLabel()`

### Frontend:
- [ ] Lägg till i `StepToolbar.tsx` kategorier
- [ ] Lägg till redigeringsformulär i `StepEditor.tsx`
- [ ] Lägg till ikon i `FlowDesigner.tsx`
- [ ] Lägg till ikon i `RpaStepNode.tsx`
- [ ] Uppdatera `VariableHelper.tsx` (om variabelskapande)
- [ ] Uppdatera `VariablesModal.tsx` (om variabelskapande)

### Testning:
- [ ] Testa stegvalidering
- [ ] Testa UI-redigering
- [ ] Testa körning i flow
- [ ] Testa variabelhantering (om tillämpligt)

## Avancerade Funktioner

### Variabelinterpolation
Systemet stöder `${variableName}` syntax i alla textfält:

```typescript
// I runner implementation
const interpolatedValue = interpolateVariables(step.value, context.variables);
```

**Stöds i:**
- Navigation: URL-fält
- Interaktion: Selectors, värden, text
- Extraktion: Selectors, attribut, filsökvägar
- Villkorliga steg: Selectors

### Credential-Integration
För steg som behöver credentials:

```typescript
// I StepEditor.tsx
const [credentials, setCredentials] = useState<Credential[]>([])

useEffect(() => {
  credentialApi.getCredentials(flow.customerId)
    .then(setCredentials)
    .catch(console.error)
}, [flow.customerId])

// Dropdown för credential-val
<select
  value={editedStep.credentialId || ''}
  onChange={(e) => updateStep({ credentialId: e.target.value })}
>
  <option value="">Välj credential</option>
  {credentials.map(cred => (
    <option key={cred.id} value={cred.id}>{cred.name}</option>
  ))}
</select>
```

### Villkorlig Logik
För steg med villkorlig körning:

```typescript
// I runner
case 'conditionalClick':
  const element = await page.locator(interpolatedSelector);
  const exists = await element.count() > 0;
  const isEnabled = exists ? await element.isEnabled() : false;

  let shouldClick = false;
  switch (step.condition) {
    case 'exists': shouldClick = exists; break;
    case 'enabled': shouldClick = isEnabled; break;
    case 'disabled': shouldClick = exists && !isEnabled; break;
  }

  if (shouldClick) {
    await element.click();
  }

  onLog({
    level: 'info',
    message: `Conditional click: ${shouldClick ? 'clicked' : 'skipped'} (${step.condition})`,
    stepId: step.id
  });
  break;
```

### Filhantering
För steg som hanterar filer:

```typescript
// Base64-konvertering
const fs = require('fs').promises;
const path = require('path');

// Läs fil och konvertera till base64
const filePath = path.join(process.cwd(), 'downloads', filename);
const fileBuffer = await fs.readFile(filePath);
const base64Data = fileBuffer.toString('base64');

// Spara som variabel
if (step.variableName) {
  variables[step.variableName] = base64Data;
  context.variables[step.variableName] = base64Data;
}
```

### Felhantering
Implementera robust felhantering:

```typescript
try {
  // Steg-logik här
  await page.click(interpolatedSelector, { timeout });

  onLog({
    level: 'info',
    message: `Step completed successfully`,
    stepId: step.id
  });
} catch (error) {
  onLog({
    level: 'error',
    message: `Step failed: ${error.message}`,
    stepId: step.id
  });
  throw error; // Re-throw för att stoppa flow
}
```

## Designmönster

### 1. Factory Pattern
Systemet använder factory pattern för runners:
- `RunnerFactory` skapar rätt runner för stegtyp
- `RunnerRegistry` hanterar registrering av runners
- Automatisk mappning från stegtyp till runner

### 2. Strategy Pattern
Olika runners för olika stegkategorier:
- `PlaywrightRunner` för web automation
- `ApiRunner` för API-anrop (framtida)
- `DatabaseRunner` för databas-operationer (framtida)

### 3. Observer Pattern
Loggning och progress tracking:
- `onLog` callback för real-time logging
- `cancellationChecker` för avbrytning
- Event-driven uppdateringar i UI

## Prestandaoptimering

### Runner-Caching
Runners cachas per execution för prestanda:

```typescript
// I RunnerRegistry
public getOrCreateRunner(executionId: string, runnerType: RunnerType): IRunner {
  const cacheKey = `${executionId}-${runnerType}`;

  let runner = this.runnerInstances.get(cacheKey);
  if (!runner) {
    runner = this.createRunner(runnerType);
    this.runnerInstances.set(cacheKey, runner);
  }

  return runner;
}
```

### Delay-Konfiguration
Centraliserad delay-konfiguration:

```typescript
// I PlaywrightRunner
private async addStepDelay(): Promise<void> {
  const delay = Math.floor(Math.random() * 9000) + 1000; // 1-10s
  await new Promise(resolve => setTimeout(resolve, delay));
}
```

## Exempel: Komplett Implementation

### Nytt Steg: "waitUntil"
```typescript
// 1. Interface (shared/src/types.ts)
export interface WaitUntilStep extends RpaStepBase {
  type: 'waitUntil';
  condition: 'visible' | 'hidden' | 'enabled' | 'disabled';
  selector: string;
  maxWait?: number; // milliseconds
}

// 2. Lägg till i union type
export type RpaStep = ... | WaitUntilStep;

// 3. Runner implementation (backend/src/runner/playwrightRunner.ts)
case 'waitUntil':
  const interpolatedWaitSelector = interpolateVariables(step.selector, context.variables);
  const maxWait = step.maxWait || 30000;

  try {
    switch (step.condition) {
      case 'visible':
        await page.waitForSelector(interpolatedWaitSelector, {
          state: 'visible',
          timeout: maxWait
        });
        break;
      case 'hidden':
        await page.waitForSelector(interpolatedWaitSelector, {
          state: 'hidden',
          timeout: maxWait
        });
        break;
      // ... andra conditions
    }

    onLog({
      level: 'info',
      message: `Wait condition met: ${step.condition} for ${interpolatedWaitSelector}`,
      stepId: step.id
    });
  } catch (error) {
    onLog({
      level: 'error',
      message: `Wait timeout: ${step.condition} for ${interpolatedWaitSelector}`,
      stepId: step.id
    });
    throw error;
  }
  break;

// 4. UI Components - se tidigare exempel
```

Se befintliga steg som `extractText`, `takeScreenshot`, eller `downloadFile` för kompletta exempel på variabelskapande steg.

För enkla interaktionssteg, se `click`, `fill`, eller `type` som exempel.
